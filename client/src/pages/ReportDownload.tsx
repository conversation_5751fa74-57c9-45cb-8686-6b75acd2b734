import { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface ReportData {
  analysisId: number;
  fullReport: any;
  reportUrl: string;
}

export default function ReportDownload() {
  const [, setLocation] = useLocation();
  const params = useParams<{ id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<ReportData | null>(null);

  useEffect(() => {
    const fetchReportData = async () => {
      try {
        // Check if we're coming from Stripe success
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = urlParams.get('session_id');

        if (sessionId) {
          // Verify payment with Stripe
          try {
            const verifyResponse = await apiRequest(
              "GET",
              `/api/report/verify-payment/${params.id}?session_id=${sessionId}`
            );

            if (verifyResponse.ok) {
              toast({
                title: "Payment Successful",
                description: "Your payment has been processed. Generating your report...",
                variant: "default"
              });

              // Remove session_id from URL
              window.history.replaceState({}, document.title, window.location.pathname);
            }
          } catch (error) {
            console.error("Error verifying payment:", error);
          }
        }

        const response = await apiRequest(
          "GET",
          `/api/report/full/${params.id}`
        );

        const responseData = await response.json();
        console.log("Report response:", responseData);

        if (response.ok && responseData.status === "success") {
          setReportData(responseData.data);
        } else if (response.status === 403) {
          // Payment required
          toast({
            title: "Payment Required",
            description: "Please complete your payment to access the full report.",
            variant: "destructive"
          });
          setLocation(`/report/payment/${params.id}`);
        } else if (response.status === 202) {
          // Report still processing
          toast({
            title: "Report Processing",
            description: "Your report is still being generated. Redirecting to processing page...",
            variant: "default"
          });
          setLocation(`/report/processing/${params.id}`);
        } else {
          console.error("Error loading report:", responseData);
          toast({
            title: "Error",
            description: "Failed to load report data",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error loading report:", error);
        toast({
          title: "Error",
          description: "Failed to load report data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchReportData();
    }
  }, [params.id, toast, setLocation]);

  const downloadPDF = async () => {
    if (!reportData?.reportUrl) {
      toast({
        title: "PDF Not Available",
        description: "The PDF version of this report is being generated. Please try again later.",
        variant: "default"
      });
      return;
    }

    try {
      // Fetch the PDF as a blob
      const response = await fetch(reportData.reportUrl, {
        method: 'GET',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get the PDF blob
      const pdfBlob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `MVP-Report-${params.id}.pdf`;

      // Trigger the download
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download Started",
        description: "Your PDF report is being downloaded.",
        variant: "default"
      });
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download the PDF. Please try again.",
        variant: "destructive"
      });
    }
  };

  const viewInBrowser = () => {
    // In a real implementation, this would navigate to an HTML view of the report
    // For now, we'll just show a message
    toast({
      title: "Browser View",
      description: "The HTML version of this report is available. This would navigate to the full report view.",
      variant: "default"
    });
  };

  const buyMoreReports = () => {
    setLocation("/");
    toast({
      title: "Package Offers",
      description: "You would be redirected to our packages page to purchase more reports at a discount.",
      variant: "default"
    });
  };

  // Skeleton component for loading states
  const Skeleton = ({ className }: { className?: string }) => (
    <div className={`animate-pulse bg-gray-200 rounded ${className}`}></div>
  );

  if (!reportData && !loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Card className="p-8">
          <h2 className="text-2xl font-bold text-center mb-4">Report Not Found</h2>
          <p className="text-center text-neutral-600">
            We couldn't find your report. Please make sure you have completed the payment process.
          </p>
          <div className="flex justify-center mt-6">
            <Button onClick={() => setLocation(`/report/payment/${params.id}`)}>
              Go to Payment
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8 text-center">
        <div className="mb-2">
          {loading ? (
            <Skeleton className="inline-block w-14 h-14 rounded-full mb-2" />
          ) : (
            <div className="inline-block p-2 bg-green-100 rounded-full mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
        </div>
        {loading ? (
          <>
            <Skeleton className="h-8 w-64 mx-auto mb-2" />
            <Skeleton className="h-5 w-96 mx-auto" />
          </>
        ) : (
          <>
            <h1 className="text-3xl font-bold text-gray-900">
              Your Report is Ready!
            </h1>
            <p className="text-lg text-gray-600 mt-2">
              Thank you for your purchase. Your full report is now available.
            </p>
          </>
        )}
      </div>

      <Card className="p-8 mb-8 bg-white shadow-lg rounded-xl">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-6 md:mb-0">
            {loading ? (
              <>
                <Skeleton className="h-7 w-64 mb-2" />
                <Skeleton className="h-4 w-80" />
              </>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">VibeComplete Premium Report</h2>
                <p className="text-gray-600">
                  Comprehensive analysis with code-level insights and personalized recommendations
                </p>
              </>
            )}
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            {loading ? (
              <>
                <Skeleton className="h-12 w-40" />
                <Skeleton className="h-12 w-36" />
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="lg"
                  className="flex items-center gap-2"
                  onClick={viewInBrowser}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  View in Browser
                </Button>

                <Button
                  size="lg"
                  className="flex items-center gap-2"
                  onClick={downloadPDF}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 002-2v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Download PDF
                </Button>
              </>
            )}
          </div>
        </div>

        <Separator className="my-6" />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {loading ? (
            <>
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <Skeleton className="h-5 w-32 mb-2" />
                <div className="space-y-1">
                  {[...Array(9)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                <Skeleton className="h-5 w-28 mb-2" />
                <div className="space-y-1">
                  {[...Array(8)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                <Skeleton className="h-5 w-24 mb-2" />
                <div className="space-y-1">
                  {[...Array(7)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <h3 className="font-semibold text-blue-800 mb-2">Report Contents</h3>
                <ul className="space-y-1 text-sm text-blue-700">
                  <li>Executive Summary</li>
                  <li>Detailed Scoring Analysis</li>
                  <li>Technology Stack Insights</li>
                  <li>Feedback Strategy Guide</li>
                  <li>Deployment Checklist</li>
                  <li>Testing Recommendations</li>
                  <li>Documentation Plan</li>
                  <li>Personalized Roadmap</li>
                  <li>Resource Library</li>
                </ul>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                <h3 className="font-semibold text-green-800 mb-2">Report Features</h3>
                <ul className="space-y-1 text-sm text-green-700">
                  <li>25+ pages of analysis</li>
                  <li>Code-level recommendations</li>
                  <li>Actionable insights</li>
                  <li>Customized roadmap</li>
                  <li>Technical debt analysis</li>
                  <li>Performance optimization tips</li>
                  <li>Security recommendations</li>
                  <li>User experience suggestions</li>
                </ul>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                <h3 className="font-semibold text-purple-800 mb-2">Next Steps</h3>
                <ul className="space-y-1 text-sm text-purple-700">
                  <li>Review the executive summary</li>
                  <li>Implement high-priority fixes</li>
                  <li>Follow the personalized roadmap</li>
                  <li>Explore recommended resources</li>
                  <li>Track your improvements</li>
                  <li>Re-analyze after major changes</li>
                  <li>Share insights with your team</li>
                </ul>
              </div>
            </>
          )}
        </div>
      </Card>

      <Card className="p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-100">
        {loading ? (
          <>
            <div className="text-center mb-4">
              <Skeleton className="h-6 w-32 mx-auto mb-2" />
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>

            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex-1">
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-3/4" />
              </div>
              <Skeleton className="h-10 w-28" />
            </div>
          </>
        ) : (
          <>
            <div className="text-center mb-4">
              <h2 className="text-xl font-bold text-indigo-900">Special Offer</h2>
              <p className="text-indigo-700">Get 3 reports for the price of 2</p>
            </div>

            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="text-indigo-800 text-sm">
                <p>Bundle and save! Analyze multiple projects or track your progress over time with our discounted package.</p>
              </div>
              <Button variant="secondary" onClick={buyMoreReports}>
                Get This Deal
              </Button>
            </div>
          </>
        )}
      </Card>
    </div>
  );
}