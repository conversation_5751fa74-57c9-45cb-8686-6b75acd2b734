import { FullReportData } from "@shared/schema";

/**
 * Convert markdown-like text to HTML
 */
function markdownToHtml(text: any): string {
  if (!text || typeof text !== 'string') return '';

  return text
    // Convert **bold** to <strong>
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Convert bullet points to list items
    .replace(/^- (.+)$/gm, '<li>$1</li>')
    // Wrap consecutive list items in <ul>
    .replace(/(<li>.*<\/li>)/g, (match) => {
      // Only wrap if not already wrapped
      if (!match.includes('<ul>')) {
        return `<ul>${match}</ul>`;
      }
      return match;
    })
    // Convert line breaks to <br> for better formatting
    .replace(/\n/g, '<br>')
    // Clean up multiple <br> tags
    .replace(/(<br>){3,}/g, '<br><br>');
}

/**
 * Convert numbered suggestions to HTML ordered list
 */
function formatSuggestions(text: any): string {
  if (!text || typeof text !== 'string') {
    console.log('formatSuggestions received non-string:', typeof text, text);
    return '';
  }

  // Check if text contains numbered items (1. 2. 3. etc.)
  const numberedPattern = /\d+\.\s+/;
  if (!numberedPattern.test(text)) {
    // No numbered items, just apply basic markdown formatting
    return markdownToHtml(text);
  }

  // Split by numbered items and create proper list
  // First, let's handle the case where numbers are at the beginning or middle of text
  const parts = text.split(/(?=\d+\.\s+)/).filter(part => part.trim().length > 0);

  if (parts.length === 0) {
    return markdownToHtml(text);
  }

  // Extract the actual content after each number
  const listItems = parts.map(part => {
    // Remove the number and dot from the beginning
    const content = part.replace(/^\d+\.\s+/, '').trim();
    return content ? `<li>${content}</li>` : '';
  }).filter(item => item.length > 0);

  if (listItems.length === 0) {
    return markdownToHtml(text);
  }

  // Create ordered list
  return `<ol>${listItems.join('')}</ol>`;
}

/**
 * Format code snippets properly
 */
function formatCodeSnippet(code: any): string {
  if (!code || typeof code !== 'string') return '';

  // Remove markdown code block markers if present
  let cleanCode = code.replace(/^```[\w]*\n?/gm, '').replace(/\n?```$/gm, '');

  // Escape HTML but preserve formatting
  cleanCode = cleanCode
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  return `<pre><code>${cleanCode}</code></pre>`;
}

/**
 * Generate HTML content for the full report using OpenAI
 */
export async function generateReportHTML(fullReport: FullReportData, projectName: string): Promise<string> {
  // Create a comprehensive HTML template with all the report sections
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVP Readiness Report - ${projectName}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        /* Browser-specific enhancements */
        .browser-actions {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .action-btn:hover {
            background: #0056b3;
        }

        .action-btn.secondary {
            background: #6c757d;
        }

        .action-btn.secondary:hover {
            background: #545b62;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #1e40af;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            color: #6b7280;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section-title {
            font-size: 1.8rem;
            color: #1e40af;
            margin-bottom: 20px;
            border-left: 4px solid #3b82f6;
            padding-left: 15px;
        }
        
        .subsection-title {
            font-size: 1.3rem;
            color: #374151;
            margin: 20px 0 10px 0;
            font-weight: 600;
        }
        
        .score-badge {
            display: inline-block;
            background: #10b981;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px 0;
        }
        
        .score-badge.low {
            background: #ef4444;
        }
        
        .score-badge.medium {
            background: #f59e0b;
        }
        
        .score-badge.high {
            background: #10b981;
        }
        
        .strengths-weaknesses {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        
        .strengths, .weaknesses {
            padding: 20px;
            border-radius: 8px;
        }
        
        .strengths {
            background: #f0fdf4;
            border-left: 4px solid #10b981;
        }
        
        .weaknesses {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
        }
        
        .code-snippet {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        .code-snippet pre {
            margin: 0;
            padding: 0;
            background: none;
            border: none;
            font-family: inherit;
            font-size: inherit;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .code-snippet code {
            background: none;
            padding: 0;
            font-family: inherit;
            font-size: inherit;
            color: #1f2937;
        }

        /* Markdown formatting */
        strong {
            font-weight: 600;
            color: #1f2937;
        }

        ul, ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        ol {
            counter-reset: item;
        }

        ol li {
            display: block;
            margin: 8px 0;
            padding-left: 5px;
            line-height: 1.5;
        }

        ul li {
            margin: 5px 0;
            line-height: 1.5;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .checklist li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            text-align: center;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e40af;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 5px;
        }
        
        .roadmap-step {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .priority-high {
            border-left: 4px solid #ef4444;
        }
        
        .priority-medium {
            border-left: 4px solid #f59e0b;
        }
        
        .priority-low {
            border-left: 4px solid #10b981;
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            .container {
                padding: 20px;
            }
            
            .section {
                page-break-inside: avoid;
                margin-bottom: 30px;
            }
            
            .strengths-weaknesses {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Browser Action Buttons -->
    <div class="browser-actions">
        <button class="action-btn" onclick="window.print()">Print Report</button>
        <a href="javascript:history.back()" class="action-btn secondary">Back to Download</a>
    </div>

    <div class="container">
        <div class="header">
            <h1>MVP Readiness Report</h1>
            <div class="subtitle">Comprehensive Analysis for ${projectName}</div>
            <div class="subtitle">Generated on ${new Date().toLocaleDateString()}</div>
        </div>

        <!-- Executive Summary Section -->
        <div class="section">
            <h2 class="section-title">Executive Summary</h2>
            <div>${markdownToHtml(fullReport.executiveSummary || 'Executive summary not available')}</div>
        </div>

        <!-- Detailed Scores Section -->
        <div class="section">
            <h2 class="section-title">Detailed Score Breakdown</h2>
            ${fullReport.detailedScores?.map(score => `
                <div class="subsection">
                    <h3 class="subsection-title">${score.name}</h3>
                    <div class="score-badge ${getScoreClass(score.score)}">${score.score}/5</div>
                    <p><strong>Explanation:</strong> ${score.explanation}</p>
                    <p><strong>Examples:</strong> ${score.examples}</p>
                    ${score.codeSnippet ? `<div class="code-snippet">${formatCodeSnippet(score.codeSnippet)}</div>` : ''}
                    <div><strong>Suggestions:</strong></div>
                    <div>${formatSuggestions(score.suggestions)}</div>
                </div>
            `).join('') || '<p>Detailed scores not available</p>'}
        </div>

        <!-- Technology Stack Analysis -->
        <div class="section">
            <h2 class="section-title">Technology Stack Analysis</h2>
            <div class="strengths-weaknesses">
                <div class="strengths">
                    <h3 class="subsection-title">Strengths</h3>
                    <ul>
                        ${fullReport.techStack?.pros?.map(pro => `<li>${pro}</li>`).join('') || '<li>No strengths identified</li>'}
                    </ul>
                </div>
                <div class="weaknesses">
                    <h3 class="subsection-title">Areas for Improvement</h3>
                    <ul>
                        ${fullReport.techStack?.cons?.map(con => `<li>${con}</li>`).join('') || '<li>No areas for improvement identified</li>'}
                    </ul>
                </div>
            </div>
            <h3 class="subsection-title">Recommendations</h3>
            <ul>
                ${fullReport.techStack?.recommendations?.map(rec => `<li>${rec}</li>`).join('') || '<li>No recommendations available</li>'}
            </ul>
        </div>

        <!-- User Feedback Strategy -->
        <div class="section">
            <h2 class="section-title">User Feedback Strategy</h2>
            <div class="score-badge ${getScoreClass(fullReport.feedbackStrategy?.currentScore || 0)}">${fullReport.feedbackStrategy?.currentScore || 0}/5</div>
            <p><strong>Current Gaps:</strong> ${fullReport.feedbackStrategy?.gaps || 'No gaps identified'}</p>
            <h3 class="subsection-title">Implementation Guide</h3>
            <p>${fullReport.feedbackStrategy?.guide || 'No guide available'}</p>
            ${fullReport.feedbackStrategy?.codeSnippet ? `
                <h3 class="subsection-title">Code Example</h3>
                <div class="code-snippet">${formatCodeSnippet(fullReport.feedbackStrategy.codeSnippet)}</div>
            ` : ''}
            <h3 class="subsection-title">Best Practices</h3>
            <ul>
                ${fullReport.feedbackStrategy?.bestPractices?.map(practice => `<li>${practice}</li>`).join('') || '<li>No best practices available</li>'}
            </ul>
        </div>

        <!-- Deployment Checklist -->
        <div class="section">
            <h2 class="section-title">Deployment & Launch Checklist</h2>
            <div class="score-badge ${getScoreClass(fullReport.deploymentChecklist?.currentScore || 0)}">${fullReport.deploymentChecklist?.currentScore || 0}/5</div>
            <ul class="checklist">
                <li><strong>Hosting:</strong> ${fullReport.deploymentChecklist?.hosting || 'Not specified'}</li>
                <li><strong>Performance:</strong> ${fullReport.deploymentChecklist?.performance || 'Not specified'}</li>
                <li><strong>Security:</strong> ${fullReport.deploymentChecklist?.security || 'Not specified'}</li>
                <li><strong>Monitoring:</strong> ${fullReport.deploymentChecklist?.monitoring || 'Not specified'}</li>
            </ul>
            <h3 class="subsection-title">Timeline</h3>
            <p>${fullReport.deploymentChecklist?.timeline || 'No timeline provided'}</p>
        </div>

        <!-- Testing Recommendations -->
        <div class="section">
            <h2 class="section-title">Testing & Stability Recommendations</h2>
            <div class="score-badge ${getScoreClass(fullReport.testingRecommendations?.currentScore || 0)}">${fullReport.testingRecommendations?.currentScore || 0}/5</div>
            <h3 class="subsection-title">Testing Gaps</h3>
            <ul>
                ${fullReport.testingRecommendations?.gaps?.map(gap => `<li>${gap}</li>`).join('') || '<li>No gaps identified</li>'}
            </ul>
            <h3 class="subsection-title">Recommended Frameworks</h3>
            <ul>
                ${fullReport.testingRecommendations?.frameworks?.map(framework => `<li>${framework}</li>`).join('') || '<li>No frameworks recommended</li>'}
            </ul>
            ${fullReport.testingRecommendations?.sampleTests ? `
                <h3 class="subsection-title">Sample Test Cases</h3>
                <div class="code-snippet">${formatCodeSnippet(fullReport.testingRecommendations.sampleTests)}</div>
            ` : ''}
        </div>

        <!-- Documentation Plan -->
        <div class="section">
            <h2 class="section-title">Documentation Enhancement Plan</h2>
            <div class="score-badge ${getScoreClass(fullReport.documentationPlan?.currentScore || 0)}">${fullReport.documentationPlan?.currentScore || 0}/5</div>
            <h3 class="subsection-title">Documentation Gaps</h3>
            <ul>
                ${fullReport.documentationPlan?.gaps?.map(gap => `<li>${gap}</li>`).join('') || '<li>No gaps identified</li>'}
            </ul>
            ${fullReport.documentationPlan?.readmeTemplate ? `
                <h3 class="subsection-title">README Template</h3>
                <div class="code-snippet">${formatCodeSnippet(fullReport.documentationPlan.readmeTemplate)}</div>
            ` : ''}
        </div>

        <!-- Competitive Benchmarking -->
        <div class="section">
            <h2 class="section-title">Competitive Benchmarking</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">${fullReport.benchmarking?.score || 0}/40</div>
                    <div class="metric-label">Your Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${fullReport.benchmarking?.averageScore || 27}/40</div>
                    <div class="metric-label">Industry Average</div>
                </div>
            </div>
            <div class="strengths-weaknesses">
                <div class="strengths">
                    <h3 class="subsection-title">Areas of Excellence</h3>
                    <ul>
                        ${fullReport.benchmarking?.strengths?.map(strength => `<li>${strength}</li>`).join('') || '<li>No strengths identified</li>'}
                    </ul>
                </div>
                <div class="weaknesses">
                    <h3 class="subsection-title">Areas to Improve</h3>
                    <ul>
                        ${fullReport.benchmarking?.weaknesses?.map(weakness => `<li>${weakness}</li>`).join('') || '<li>No weaknesses identified</li>'}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Resource Library -->
        <div class="section">
            <h2 class="section-title">Resource Library</h2>
            <div class="strengths-weaknesses">
                <div class="strengths">
                    <h3 class="subsection-title">Tutorials</h3>
                    <ul>
                        ${fullReport.resourceLibrary?.tutorials?.map(tutorial => `<li>${tutorial}</li>`).join('') || '<li>No tutorials available</li>'}
                    </ul>
                    <h3 class="subsection-title">Tools</h3>
                    <ul>
                        ${fullReport.resourceLibrary?.tools?.map(tool => `<li>${tool}</li>`).join('') || '<li>No tools recommended</li>'}
                    </ul>
                </div>
                <div class="weaknesses">
                    <h3 class="subsection-title">Communities</h3>
                    <ul>
                        ${fullReport.resourceLibrary?.communities?.map(community => `<li>${community}</li>`).join('') || '<li>No communities listed</li>'}
                    </ul>
                    <h3 class="subsection-title">Templates</h3>
                    <ul>
                        ${fullReport.resourceLibrary?.templates?.map(template => `<li>${template}</li>`).join('') || '<li>No templates available</li>'}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Personalized Roadmap -->
        <div class="section">
            <h2 class="section-title">Personalized Roadmap</h2>
            <p><strong>Current Level:</strong> ${fullReport.roadmap?.currentLevel || 'Not specified'}</p>
            <p><strong>Target Level:</strong> ${fullReport.roadmap?.targetLevel || 'Not specified'}</p>
            <h3 class="subsection-title">Action Steps</h3>
            ${fullReport.roadmap?.steps?.map(step => `
                <div class="roadmap-step priority-${step.priority?.toLowerCase() || 'medium'}">
                    <h4>${step.title}</h4>
                    <p><strong>Priority:</strong> ${step.priority} | <strong>Estimated Time:</strong> ${step.estimatedTime}</p>
                </div>
            `).join('') || '<p>No roadmap steps available</p>'}
            <h3 class="subsection-title">Timeline</h3>
            <p>${fullReport.roadmap?.timeline || 'No timeline provided'}</p>
        </div>

        <!-- Visualizations & Metrics -->
        <div class="section">
            <h2 class="section-title">Key Metrics</h2>
            <h3 class="subsection-title">Code Quality Metrics</h3>
            <div class="metrics-grid">
                ${Object.entries(fullReport.visualizations?.codeQualityMetrics || {}).map(([key, value]) => `
                    <div class="metric-card">
                        <div class="metric-value">${value}</div>
                        <div class="metric-label">${key}</div>
                    </div>
                `).join('')}
            </div>
            <!-- Performance Metrics Section - Commented out since we can't measure performance from static code -->
            <!--
            <h3 class="subsection-title">Performance Metrics</h3>
            <div class="metrics-grid">
                ${Object.entries(fullReport.visualizations?.performanceMetrics || {}).map(([key, value]) => `
                    <div class="metric-card">
                        <div class="metric-value">${value}</div>
                        <div class="metric-label">${key}</div>
                    </div>
                `).join('')}
            </div>
            -->
        </div>

        <div class="footer">
            <p>Generated by VibeComplete - AI Code Analysis Platform</p>
            <p>Report generated on ${new Date().toLocaleString()}</p>
        </div>
    </div>
</body>
</html>
  `;

  return html;
}

/**
 * Helper function to determine score class for styling
 */
function getScoreClass(score: number): string {
  if (score <= 2) return 'low';
  if (score <= 3) return 'medium';
  return 'high';
}

/**
 * Helper function to escape HTML characters (unused but kept for reference)
 */
/*
function escapeHtml(text: any): string {
  if (text === null || text === undefined) {
    return '';
  }

  const str = String(text);
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/\n/g, '<br>');
}
*/
