import { objectStorage } from "./object-storage";

async function testObjectStorage() {
  console.log("Testing Replit Object Storage integration...");
  
  try {
    // Test 1: Upload a test file
    const testBuffer = Buffer.from("Hello, this is a test file for MVP Score Widget!");
    const testFilename = await objectStorage.uploadFile(testBuffer, 999, "txt");
    console.log("✅ File uploaded successfully:", testFilename);
    
    // Test 2: Check if file exists
    const exists = await objectStorage.fileExists(testFilename);
    console.log("✅ File exists check:", exists);
    
    // Test 3: Download the file
    const downloadedBuffer = await objectStorage.downloadFile(testFilename);
    const downloadedContent = downloadedBuffer.toString();
    console.log("✅ File downloaded successfully:", downloadedContent);
    
    // Test 4: List files
    const files = await objectStorage.listFiles("uploads/");
    console.log("✅ Files in uploads/ directory:", files.length);
    
    // Test 5: Delete the test file
    const deleted = await objectStorage.deleteFile(testFilename);
    console.log("✅ File deleted successfully:", deleted);
    
    console.log("🎉 All object storage tests passed!");
    
  } catch (error) {
    console.error("❌ Object storage test failed:", error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testObjectStorage();
}

export { testObjectStorage };
