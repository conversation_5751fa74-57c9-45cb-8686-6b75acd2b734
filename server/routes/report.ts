import { Router, Request, Response } from "express";
import { storage } from "../db-storage";
import { generateFullReport, generateAndSaveReportPDF } from "../report-generator";
import { pdfExists, getPDFPath, generatePDFBuffer } from "../pdf-generator";
import { createCheckoutSession, verifyCheckoutSession, handleWebhookEvent } from "../stripe-service";
import { objectStorage } from "../object-storage";
import { generateReportHTML } from "../html-generator";
import fs from "fs";

const router = Router();

// Generate a full report for an analysis
router.post("/generate/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    
    // Check if analysis exists
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({ 
        status: "error",
        message: "Analysis not found" 
      });
    }
    
    // Check if analysis is completed
    if (analysis.status !== "completed") {
      return res.status(400).json({ 
        status: "error",
        message: "Analysis is not completed yet" 
      });
    }
    
    // Generate full report
    const fullReport = await generateFullReport(analysis);
    
    // Update analysis with full report
    await storage.updateFullReport(analysisId, fullReport);
    
    // Generate and save PDF report
    const projectName = analysis.projectInfo?.name || `Analysis ${analysisId}`;
    const reportUrl = await generateAndSaveReportPDF(fullReport, analysisId, projectName);

    if (reportUrl) {
      await storage.updateReportUrl(analysisId, reportUrl);

      // If it's an object storage URL, also store the object key
      if (reportUrl.includes('/download-from-storage/')) {
        const objectKey = reportUrl.split('/').pop();
        if (objectKey) {
          await storage.updateReportObjectKey(analysisId, decodeURIComponent(objectKey));
        }
      }

      console.log(`PDF generated and saved for analysis #${analysisId}`);
    } else {
      console.error(`Failed to generate PDF for analysis #${analysisId}`);
    }
    
    return res.status(200).json({
      status: "success",
      message: "Full report generated successfully",
      data: {
        analysisId,
        fullReport
      }
    });
  } catch (error: any) {
    console.error("Error generating full report:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to generate full report",
      error: error.message || String(error)
    });
  }
});

// Get HTML version of the full report for browser viewing
router.get("/html/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);

    // Check if analysis exists
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).send(`
        <html>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>Report Not Found</h1>
            <p>The requested analysis could not be found.</p>
          </body>
        </html>
      `);
    }

    // Check payment status first
    if (analysis.paymentStatus !== "completed") {
      return res.status(403).send(`
        <html>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>Payment Required</h1>
            <p>Please complete payment to access the full report.</p>
            <a href="/report/payment/${analysisId}" style="color: #007bff;">Complete Payment</a>
          </body>
        </html>
      `);
    }

    // Check if full report exists
    if (!analysis.fullReport) {
      return res.status(202).send(`
        <html>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1>Report Processing</h1>
            <p>Your report is being generated. Please wait...</p>
            <script>setTimeout(() => window.location.reload(), 5000);</script>
          </body>
        </html>
      `);
    }

    // Generate HTML content
    const projectName = analysis.projectInfo?.name || `Analysis ${analysisId}`;
    const htmlContent = await generateReportHTML(analysis.fullReport, projectName);

    // Set content type and send HTML
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(htmlContent);

  } catch (error: any) {
    console.error("Error retrieving HTML report:", error);
    res.status(500).send(`
      <html>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>Error</h1>
          <p>Failed to generate report: ${error.message || 'Unknown error'}</p>
        </body>
      </html>
    `);
  }
});

// Get a full report for an analysis
router.get("/full/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    
    // Check if analysis exists
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({ 
        status: "error",
        message: "Analysis not found" 
      });
    }
    
    // Check payment status first
    if (analysis.paymentStatus !== "completed") {
      return res.status(403).json({
        status: "error",
        message: "Please complete payment to access the full report"
      });
    }

    // Check if full report exists
    if (!analysis.fullReport || !analysis.reportUrl) {
      // Payment completed but report still processing
      return res.status(202).json({
        status: "processing",
        message: "Your report is being generated. Please wait..."
      });
    }
    
    // Return the full report data
    return res.status(200).json({
      status: "success",
      data: {
        analysisId,
        fullReport: analysis.fullReport,
        reportUrl: analysis.reportUrl
      }
    });
  } catch (error: any) {
    console.error("Error retrieving full report:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to retrieve full report",
      error: error.message || String(error)
    });
  }
});

// Create Stripe checkout session for payment
router.post("/payment/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);

    // Check if analysis exists
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({
        status: "error",
        message: "Analysis not found"
      });
    }

    // Check if analysis has basic results (needed for payment)
    if (!analysis.projectInfo || !analysis.scoreData) {
      return res.status(400).json({
        status: "error",
        message: "Analysis is not completed yet. Please wait for analysis to finish."
      });
    }

    // Check if already paid
    if (analysis.paymentStatus === "completed") {
      return res.status(400).json({
        status: "error",
        message: "Payment already completed for this analysis"
      });
    }

    // Create Stripe checkout session
    const projectName = analysis.projectInfo?.name || `Analysis ${analysisId}`;
    const customerEmail = req.body.email; // Optional customer email

    const checkoutUrl = await createCheckoutSession({
      analysisId,
      projectName,
      customerEmail
    });

    return res.status(200).json({
      status: "success",
      message: "Checkout session created",
      data: {
        checkoutUrl,
        analysisId
      }
    });
  } catch (error: any) {
    console.error("Error creating checkout session:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to create checkout session",
      error: error.message || String(error)
    });
  }
});

// Get report preview (teaser content) for an analysis
router.get("/preview/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    
    // Check if analysis exists
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({ 
        status: "error",
        message: "Analysis not found" 
      });
    }
    
    // Generate preview content based on the analysis results
    const teaserContent = {
      analysisId,
      projectName: analysis.projectInfo?.name || "Your Project",
      scorePreview: {
        total: analysis.scoreData?.total || 0,
        category: analysis.scoreData?.category || "Unknown",
        topStrength: analysis.scoreData?.strengths?.[0] || "Not available",
        topWeakness: analysis.scoreData?.weaknesses?.[0] || "Not available",
      },
      testimonial: "This report saved me 8 hours of debugging—worth every penny!",
      article: {
        title: "Top 5 Mistakes Vibe Coders Make When Launching an MVP",
        snippet: "Learn how to avoid common pitfalls that can delay your MVP launch..."
      },
      tip: "Based on your project, we recommend focusing on user feedback mechanisms to improve your MVP readiness."
    };
    
    return res.status(200).json({
      status: "success",
      data: teaserContent
    });
  } catch (error: any) {
    console.error("Error retrieving report preview:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to retrieve report preview",
      error: error.message || String(error)
    });
  }
});

// Generate a personalized tip based on user's goals
router.post("/generate-tip", async (req: Request, res: Response) => {
  try {
    const { projectId, formData } = req.body;
    
    // Check if analysis exists
    const analysis = await storage.getAnalysis(parseInt(projectId));
    if (!analysis) {
      return res.status(404).json({ 
        status: "error",
        message: "Analysis not found" 
      });
    }
    
    // Generate personalized tip based on form data and analysis results
    let tip = "Based on your goals, we recommend focusing on ";
    
    if (formData.primaryGoal === "User acquisition") {
      tip += "responsive design and clear user onboarding flows.";
    } else if (formData.primaryGoal === "Technical stability") {
      tip += "implementing automated tests and error monitoring.";
    } else if (formData.primaryGoal === "Launch preparation") {
      tip += "setting up analytics and user feedback mechanisms.";
    } else {
      tip += "completing your core functionality before adding additional features.";
    }
    
    return res.status(200).json({
      status: "success",
      data: {
        tip
      }
    });
  } catch (error: any) {
    console.error("Error generating personalized tip:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to generate personalized tip",
      error: error.message || String(error)
    });
  }
});

// Stripe webhook endpoint
router.post("/webhook", async (req: Request, res: Response) => {
  try {
    const signature = req.headers['stripe-signature'] as string;

    if (!signature) {
      return res.status(400).json({ error: 'Missing stripe-signature header' });
    }

    const result = await handleWebhookEvent(req.body, signature);

    if (result.success && result.analysisId && result.eventType === 'checkout.session.completed') {
      // Payment was successful, generate the report
      try {
        const analysis = await storage.getAnalysis(result.analysisId);
        if (analysis) {
          // Update payment status
          await storage.updatePaymentStatus(result.analysisId, "completed");

          // Generate the full report
          if (!analysis.fullReport) {
            const fullReport = await generateFullReport(analysis);
            await storage.updateFullReport(result.analysisId, fullReport);

            // Generate and save PDF
            const projectName = analysis.projectInfo?.name || `Analysis ${result.analysisId}`;
            const reportUrl = await generateAndSaveReportPDF(fullReport, result.analysisId, projectName);

            if (reportUrl) {
              await storage.updateReportUrl(result.analysisId, reportUrl);

              // If it's an object storage URL, also store the object key
              if (reportUrl.includes('/download-from-storage/')) {
                const objectKey = reportUrl.split('/').pop();
                if (objectKey) {
                  await storage.updateReportObjectKey(result.analysisId, decodeURIComponent(objectKey));
                }
              }

              console.log(`PDF generated after payment for analysis #${result.analysisId}`);
            }
          }
        }
      } catch (error) {
        console.error('Error processing payment webhook:', error);
        // Don't fail the webhook, just log the error
      }
    }

    res.status(200).json({ received: true });
  } catch (error: any) {
    console.error('Webhook error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Check processing status
router.get("/status/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);

    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({
        status: "error",
        message: "Analysis not found"
      });
    }

    // Check if payment is required
    if (analysis.paymentStatus !== "completed") {
      return res.status(403).json({
        status: "error",
        message: "Payment required"
      });
    }

    return res.status(200).json({
      status: "success",
      data: {
        pdfReady: !!analysis.reportUrl,
        paymentStatus: analysis.paymentStatus,
        fullReportGenerated: !!analysis.fullReport,
        analysisCompleted: analysis.status === 'completed'
      }
    });
  } catch (error: any) {
    console.error("Error checking status:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to check status",
      error: error.message
    });
  }
});

// Verify payment and redirect
router.get("/verify-payment/:id", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    const sessionId = req.query.session_id as string;

    if (!sessionId) {
      return res.status(400).json({
        status: "error",
        message: "Missing session_id parameter"
      });
    }

    const verification = await verifyCheckoutSession(sessionId);

    if (verification.success && verification.analysisId === analysisId) {
      // Update payment status if not already done by webhook
      await storage.updatePaymentStatus(analysisId, "completed");

      return res.status(200).json({
        status: "success",
        message: "Payment verified successfully",
        data: {
          analysisId,
          paymentIntentId: verification.paymentIntentId
        }
      });
    } else {
      return res.status(400).json({
        status: "error",
        message: "Payment verification failed"
      });
    }
  } catch (error: any) {
    console.error("Error verifying payment:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to verify payment",
      error: error.message
    });
  }
});

// Download PDF report from object storage with fallback
router.get("/download-from-storage/:id/:objectKey", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    const objectKey = decodeURIComponent(req.params.objectKey);

    // Check if analysis exists and user has paid
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({
        status: "error",
        message: "Analysis not found"
      });
    }

    if (analysis.paymentStatus !== "completed") {
      return res.status(403).json({
        status: "error",
        message: "Payment required to download report"
      });
    }

    try {
      // Try to download from object storage
      const pdfBuffer = await objectStorage.downloadFile(objectKey);

      // Set headers for PDF download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="MVP-Report-${analysisId}.pdf"`);

      // Send the buffer as binary data
      res.end(pdfBuffer);

    } catch (objectStorageError) {
      console.error('Object storage download failed, attempting regeneration:', objectStorageError);

      // Fallback: Regenerate PDF from stored fullReport
      if (analysis.fullReport) {
        try {
          const projectName = analysis.projectInfo?.name || `Analysis ${analysisId}`;
          const pdfBuffer = await generatePDFBuffer(analysis.fullReport, analysisId, projectName);

          if (pdfBuffer) {
            // Try to re-upload to object storage for future requests
            try {
              await objectStorage.uploadPDF(pdfBuffer, analysisId);
            } catch (reuploadError) {
              console.error('Re-upload to object storage failed:', reuploadError);
            }

            // Set headers and send regenerated PDF
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="MVP-Report-${analysisId}.pdf"`);
            res.end(pdfBuffer);
            return;
          }
        } catch (regenerationError) {
          console.error('PDF regeneration failed:', regenerationError);
        }
      }

      // Final fallback: return error
      return res.status(404).json({
        status: "error",
        message: "Report file not found and could not be regenerated"
      });
    }

  } catch (error: any) {
    console.error("Error downloading report from object storage:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to download report",
      error: error.message || String(error)
    });
  }
});

// Download PDF report (legacy local file system)
router.get("/download/:id/:filename", async (req: Request, res: Response) => {
  try {
    const analysisId = parseInt(req.params.id);
    const filename = req.params.filename;

    // Check if analysis exists and user has paid
    const analysis = await storage.getAnalysis(analysisId);
    if (!analysis) {
      return res.status(404).json({
        status: "error",
        message: "Analysis not found"
      });
    }

    if (analysis.paymentStatus !== "completed") {
      return res.status(403).json({
        status: "error",
        message: "Payment required to download report"
      });
    }

    // Check if file exists and get path
    if (!pdfExists(filename)) {
      return res.status(404).json({
        status: "error",
        message: "Report file not found"
      });
    }

    // Set headers for PDF download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="MVP-Report-${analysisId}.pdf"`);

    // Stream the file
    const filePath = getPDFPath(filename);
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error: any) {
    console.error("Error downloading report:", error);
    return res.status(500).json({
      status: "error",
      message: "Failed to download report",
      error: error.message || String(error)
    });
  }
});

// Test PDF generation endpoint
router.post("/test-pdf", async (_req: Request, res: Response) => {
  try {
    // Create a mock full report for testing
    const mockReport = {
      executiveSummary: "**Executive Summary**\n\n**Project**: Test Project, Web Application, 24/40\n\n**MVP Readiness**: Early MVP - Your project has basic viability but needs more development before release.",
      detailedScores: [
        {
          name: "Feedback Collection",
          score: 2,
          explanation: "There is no clear mechanism for collecting user feedback.",
          examples: "No feedback forms or endpoints detected.",
          suggestions: "Implement a simple feedback form using React Hook Form."
        }
      ],
      techStack: {
        technologies: ["React", "Node.js", "TypeScript"],
        pros: ["Modern tech stack", "Type safety with TypeScript"],
        cons: ["Large bundle size", "Complex setup"],
        recommendations: ["Optimize bundle size", "Add lazy loading"],
        alternatives: ["Vue.js", "Svelte"]
      },
      feedbackStrategy: {
        currentScore: 2,
        gaps: "No visible feedback mechanism detected",
        guide: "1. Create feedback form 2. Add backend endpoint 3. Store in database",
        codeSnippet: "// Example feedback form\nconst FeedbackForm = () => {\n  return <form>...</form>;\n};",
        bestPractices: ["Keep surveys short", "Offer incentives"],
        tools: ["Hotjar", "Google Analytics"]
      },
      deploymentChecklist: {
        currentScore: 3,
        gaps: "Ready for beta but needs testing",
        hosting: "Deploy on Vercel",
        performance: "Minify CSS/JS",
        security: "Add helmet.js",
        monitoring: "Integrate Sentry",
        timeline: "2 weeks for deployment"
      },
      testingRecommendations: {
        currentScore: 2,
        gaps: ["No unit tests", "No E2E tests"],
        frameworks: ["Jest", "Cypress"],
        sampleTests: "test('user login', () => {\n  expect(login(user)).toBe(true);\n});",
        userTestingPlan: "Recruit 5 beta testers"
      },
      documentationPlan: {
        currentScore: 2,
        gaps: ["Missing README", "No API docs"],
        readmeTemplate: "# Project\n## Installation\n## Usage",
        tools: ["Docusaurus"],
        examples: "Add getting-started.md"
      },
      benchmarking: {
        score: 24,
        averageScore: 27,
        strengths: ["Good UI consistency"],
        weaknesses: ["Low test coverage"]
      },
      resourceLibrary: {
        tutorials: ["React Tutorial"],
        tools: ["Jest", "Cypress"],
        communities: ["React Discord"],
        templates: ["CRA Template"]
      },
      roadmap: {
        currentLevel: "Early MVP",
        targetLevel: "Solid MVP",
        steps: [
          { title: "Add feedback form", priority: "High" as const, estimatedTime: "2 hours" },
          { title: "Write tests", priority: "Medium" as const, estimatedTime: "4 hours" }
        ],
        timeline: "2 weeks to reach Solid MVP"
      },
      visualizations: {
        radarChartData: {},
        codeQualityMetrics: { "Code Coverage": "40%", "Complexity": 5 },
        performanceMetrics: { "Load Time": "3.2s" },
        comparisonData: {}
      }
    };

    const reportUrl = await generateAndSaveReportPDF(mockReport, 999, "Test Project");

    if (reportUrl) {
      res.json({
        status: "success",
        message: "Test PDF generated successfully",
        reportUrl
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Failed to generate test PDF"
      });
    }
  } catch (error: any) {
    console.error("Error generating test PDF:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to generate test PDF",
      error: error.message
    });
  }
});

export default router;