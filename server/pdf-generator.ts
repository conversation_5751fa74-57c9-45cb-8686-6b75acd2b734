import puppeteer from "puppeteer";
import {
  getInstalledBrowsers,
} from "@puppeteer/browsers";
import { FullReportData } from "@shared/schema";
import { generateReportHTML } from "./html-generator";
import { objectStorage } from "./object-storage";
import fs from "fs";
import path from "path";
import { nanoid } from "nanoid";

// Fallback PDF generation
import * as htmlPdf from "html-pdf-node";

/**
 * Generate a PDF from report data and save it with object storage fallback
 */
export async function generateAndSavePDF(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project",
): Promise<string | null> {
  try {
    console.log(`Starting PDF generation for analysis #${analysisId}`);

    // Generate the PDF buffer
    const pdfBuffer = await generatePDFBuffer(
      fullReport,
      analysisId,
      projectName,
    );
    if (!pdfBuffer) {
      return null;
    }

    // Try to save to object storage first
    return await savePDFWithFallback(pdfBuffer, analysisId);
  } catch (error) {
    console.error(
      `Error in generateAndSavePDF for analysis #${analysisId}:`,
      error,
    );
    return null;
  }
}

/**
 * Save PDF to object storage with local disk fallback
 */
export async function savePDFWithFallback(
  pdfBuffer: Buffer,
  analysisId: number,
): Promise<string | null> {
  try {
    // Try object storage first
    const objectKey = await objectStorage.uploadPDF(pdfBuffer, analysisId);
    console.log(`PDF uploaded to object storage: ${objectKey}`);
    return `/api/report/download-from-storage/${analysisId}/${encodeURIComponent(objectKey)}`;
  } catch (objectStorageError) {
    console.error(
      "Object storage upload failed, falling back to local disk:",
      objectStorageError,
    );

    // Fallback to local disk storage
    try {
      const localUrl = await savePDFToDisk(pdfBuffer, analysisId);
      console.log(`PDF saved to local disk as fallback: ${localUrl}`);
      return localUrl;
    } catch (diskError) {
      console.error("Local disk fallback also failed:", diskError);
      return null;
    }
  }
}

/**
 * Generate PDF buffer using Puppeteer
 */
export async function generatePDFBuffer(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project",
): Promise<Buffer | null> {
  let browser;

  try {
    // Generate HTML content for the report
    const htmlContent = await generateReportHTML(fullReport, projectName);

    // Import the function

    // Find the path to Chromium dynamically
    const cacheDir = path.resolve(process.cwd());

    // 1. Get a list of browsers from that specific directory
    const installedBrowsers = await getInstalledBrowsers({ cacheDir });
    console.log("Installed browsers:", installedBrowsers);

    // 2. Find the Chrome browser from the list
    const chromeInfo = installedBrowsers.find(
      (browser) => browser.browser === "chrome",
    );

    if (!chromeInfo) {
      throw new Error(
        "Could not find a downloaded Chrome browser. Please run `npx @puppeteer/browsers install chrome`.",
      );
    }

    const chromePath = chromeInfo.executablePath;
    // const chromePath = computeExecutablePath({
    //   cacheDir: null,
    //   browser: chromeInfo.browser,
    //   buildId: chromeInfo.buildId,
    // });

    // Try multiple browser launch strategies
    const launchOptions = {
      executablePath: chromePath,
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        // "--disable-accelerated-2d-canvas",
        // "--no-first-run",
        // "--no-zygote",
        "--single-process",
        "--disable-gpu",
        // "--disable-web-security",
        // "--disable-features=VizDisplayCompositor",
        // "--disable-background-timer-throttling",
        // "--disable-backgrounding-occluded-windows",
        // "--disable-renderer-backgrounding",
        // "--disable-extensions",
        // "--disable-plugins",
        // "--disable-default-apps",
        // "--disable-translate",
        // "--disable-sync",
        // "--hide-scrollbars",
        // "--mute-audio",
        // "--no-default-browser-check",
        // "--no-pings",
        // "--disable-ipc-flooding-protection",
        // // Additional args for cloud environments
        // "--disable-background-networking",
        // "--metrics-recording-only",
        // "--no-first-run",
        // "--safebrowsing-disable-auto-update",
        // "--disable-client-side-phishing-detection",
        // "--disable-component-extensions-with-background-pages",
        // "--disable-features=TranslateUI",
        // "--disable-hang-monitor",
        // "--disable-popup-blocking",
        // "--force-color-profile=srgb",
        // "--disable-prompt-on-repost",
        // "--password-store=basic",
        // "--use-mock-keychain",
      ],
      timeout: 120000, // Increased timeout for cloud environments
      protocolTimeout: 120000, // Add protocol timeout
      slowMo: 100, // Add slight delay between operations
    };

    // Try with custom executable path first
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      console.log(
        `Getting Chromium at: ${process.env.PUPPETEER_EXECUTABLE_PATH}`,
      );
      try {
        browser = await puppeteer.launch({
          ...launchOptions,
          executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
        });
      } catch (error) {
        console.log("Custom executable path failed, trying default...");
      }
    }

    // If custom path failed or not set, try default
    if (!browser) {
      console.log(
        `Launching Puppeteer using: ${JSON.stringify(launchOptions)}`,
      );
      browser = await puppeteer.launch(launchOptions);
    }

    console.log(`Getting new Page with browser: ${JSON.stringify(browser)}`);

    const page = await browser.newPage();

    // Set the HTML content

    console.log(`Setting content from HTML`);
    await page.setContent(htmlContent, {
      waitUntil: "domcontentloaded",
    });
    console.log(`Generating PDF...`);
    // Generate PDF with proper formatting
    const pdfBuffer = await page.pdf({
      format: "A4",
      printBackground: true,
      margin: {
        top: "20mm",
        right: "15mm",
        bottom: "20mm",
        left: "15mm",
      },
      displayHeaderFooter: true,
      headerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          MVP Readiness Report - ${projectName}
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          Page <span class="pageNumber"></span> of <span class="totalPages"></span>
        </div>
      `,
    });

    // Close the browser
    await browser.close();

    console.log(`PDF generation completed for analysis #${analysisId}`);
    return Buffer.from(pdfBuffer);
  } catch (error) {
    console.error(
      `Error generating PDF buffer for analysis #${analysisId}:`,
      error,
    );

    // Log specific Chrome/Puppeteer errors
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (
      errorMessage.includes("libnspr4.so") ||
      errorMessage.includes("shared libraries")
    ) {
      console.error(
        "Chrome dependencies missing. Trying fallback PDF generation...",
      );

      // Try fallback PDF generation
      try {
        console.log(
          `Attempting fallback PDF generation for analysis #${analysisId}`,
        );
        return await generatePDFBufferFallback(
          fullReport,
          analysisId,
          projectName,
        );
      } catch (fallbackError) {
        console.error("Fallback PDF generation also failed:", fallbackError);
        console.error("Please install Chrome dependencies:");
        console.error(
          "sudo apt-get install -y libnss3 libnspr4 libatk-bridge2.0-0 libdrm2 libxkbcommon0 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2",
        );
      }
    }

    return null;
  } finally {
    // Ensure browser is closed
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error("Error closing browser:", closeError);
      }
    }
  }
}

/**
 * Fallback PDF generation using html-pdf-node (doesn't require Chrome)
 */
async function generatePDFBufferFallback(
  fullReport: FullReportData,
  analysisId: number,
  projectName: string = "Unknown Project",
): Promise<Buffer | null> {
  try {
    // Generate HTML content for the report
    const htmlContent = await generateReportHTML(fullReport, projectName);

    // Configure html-pdf-node options
    const options = {
      format: "A4",
      margin: {
        top: "20mm",
        right: "15mm",
        bottom: "20mm",
        left: "15mm",
      },
      printBackground: true,
      displayHeaderFooter: true,
      headerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          MVP Readiness Report - ${projectName}
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
          Page <span class="pageNumber"></span> of <span class="totalPages"></span>
        </div>
      `,
    };

    // Generate PDF using html-pdf-node
    const file = { content: htmlContent };
    const pdfBuffer = await htmlPdf.generatePdf(file, options);

    console.log(
      `Fallback PDF generated successfully for analysis #${analysisId}`,
    );
    return pdfBuffer;
  } catch (error) {
    console.error(
      `Error in fallback PDF generation for analysis #${analysisId}:`,
      error,
    );
    throw error;
  }
}

/**
 * Save PDF buffer to disk and return download URL
 */
export async function savePDFToDisk(
  pdfBuffer: Buffer,
  analysisId: number,
): Promise<string | null> {
  try {
    const reportsDir = path.join(process.cwd(), "reports");
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const fileName = `report-${analysisId}-${nanoid()}.pdf`;
    const filePath = path.join(reportsDir, fileName);
    fs.writeFileSync(filePath, pdfBuffer);

    const reportUrl = `/api/report/download/${analysisId}/${fileName}`;
    console.log(`PDF saved successfully: ${filePath}`);

    return reportUrl;
  } catch (error) {
    console.error("Error saving PDF to disk:", error);
    return null;
  }
}

/**
 * Check if PDF file exists
 */
export function pdfExists(filename: string): boolean {
  const filePath = path.join(process.cwd(), "reports", filename);
  return fs.existsSync(filePath);
}

/**
 * Get PDF file path
 */
export function getPDFPath(filename: string): string {
  return path.join(process.cwd(), "reports", filename);
}
