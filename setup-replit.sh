#!/bin/bash

# Replit Setup Script for Puppeteer PDF Generation
echo "Setting up Replit environment for Puppeteer..."

# Check if Chromium is available
echo "Checking for Chromium installation..."
if command -v chromium &> /dev/null; then
    echo "✓ chromium found at: $(which chromium)"
    export PUPPETEER_EXECUTABLE_PATH=$(which chromium)
elif command -v chromium-browser &> /dev/null; then
    echo "✓ chromium-browser found at: $(which chromium-browser)"
    export PUPPETEER_EXECUTABLE_PATH=$(which chromium-browser)
elif command -v google-chrome-stable &> /dev/null; then
    echo "✓ google-chrome-stable found at: $(which google-chrome-stable)"
    export PUPPETEER_EXECUTABLE_PATH=$(which google-chrome-stable)
elif command -v google-chrome &> /dev/null; then
    echo "✓ google-chrome found at: $(which google-chrome)"
    export PUPPETEER_EXECUTABLE_PATH=$(which google-chrome)
else
    echo "❌ No Chromium/Chrome installation found!"
    echo "Please ensure Chromium is installed in your replit.nix file"
    exit 1
fi

# Test Chrome execution
echo "Testing Chrome execution..."
if $PUPPETEER_EXECUTABLE_PATH --version &> /dev/null; then
    echo "✓ Chrome executes successfully"
    echo "Chrome version: $($PUPPETEER_EXECUTABLE_PATH --version)"
else
    echo "❌ Chrome execution failed"
    echo "This might indicate missing dependencies"
fi

# Set environment variables
echo "Setting environment variables..."
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
export CHROME_NO_SANDBOX=true
export NODE_OPTIONS="--max-old-space-size=2048"
export DISPLAY=:99

# Test a simple Chrome launch
echo "Testing Chrome launch with Puppeteer args..."
timeout 30s $PUPPETEER_EXECUTABLE_PATH \
    --headless \
    --no-sandbox \
    --disable-setuid-sandbox \
    --disable-dev-shm-usage \
    --single-process \
    --disable-gpu \
    --disable-web-security \
    --virtual-time-budget=1000 \
    --run-all-compositor-stages-before-draw \
    --dump-dom \
    about:blank > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✓ Chrome launches successfully with Puppeteer args"
else
    echo "❌ Chrome launch with Puppeteer args failed"
    echo "Enabling fallback mode..."
    export PUPPETEER_FALLBACK_MODE=true
fi

echo "Environment setup complete!"
echo "PUPPETEER_EXECUTABLE_PATH: $PUPPETEER_EXECUTABLE_PATH"
echo "PUPPETEER_FALLBACK_MODE: ${PUPPETEER_FALLBACK_MODE:-false}"

# Create a .env file with the settings
cat > .env.replit << EOF
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
PUPPETEER_EXECUTABLE_PATH=$PUPPETEER_EXECUTABLE_PATH
CHROME_NO_SANDBOX=true
NODE_OPTIONS=--max-old-space-size=2048
DISPLAY=:99
${PUPPETEER_FALLBACK_MODE:+PUPPETEER_FALLBACK_MODE=true}
EOF

echo "Environment variables saved to .env.replit"
echo "You can source this file with: source .env.replit"
